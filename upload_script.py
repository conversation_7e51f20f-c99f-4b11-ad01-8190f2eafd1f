import requests
import json
import os

def UploadFile(file_path, meta):
    """
    Upload a file (image or video) to the API
    
    Args:
        file_path (str): Path to the file to upload
        meta (dict): Metadata for the file
    
    Returns:
        tuple: (status_code, response_data)
    """
    api_url = "https://tcdn.sdk.li/v1/upload"
    meta_str = json.dumps(meta)
    
    # Check if file exists
    if not os.path.exists(file_path):
        return None, f"File not found: {file_path}"
    
    try:
        with open(file_path, "rb") as file:
            files = {"file": file}
            data = {"meta": meta_str}
            res = requests.post(api_url, files=files, data=data)
        
        # Try to parse JSON response
        try:
            return res.status_code, res.json()
        except Exception:
            return res.status_code, res.text
            
    except Exception as e:
        return None, f"Error uploading file: {str(e)}"

def UploadVideo(video_path, meta):
    """
    Specific function for uploading videos
    
    Args:
        video_path (str): Path to the video file
        meta (dict): Metadata for the video
    
    Returns:
        tuple: (status_code, response_data)
    """
    return UploadFile(video_path, meta)

def UploadImage(image_path, meta):
    """
    Specific function for uploading images
    
    Args:
        image_path (str): Path to the image file
        meta (dict): Metadata for the image
    
    Returns:
        tuple: (status_code, response_data)
    """
    return UploadFile(image_path, meta)

# Example usage for image upload
def upload_image_example():
    status, response = UploadImage("output.png", {"tag": "test", "description": "Sample image upload"})
    print("Image Upload Response:", response)
    print("Status Code:", status)

# Example usage for video upload
def upload_video_example():
    # Replace with your actual video file name
    video_file = "sample_video.mp4"  # Change this to your video file name
    
    meta_data = {
        "tag": "video_test",
        "description": "Sample video upload",
        "type": "video",
        "category": "demo"
    }
    
    status, response = UploadVideo(video_file, meta_data)
    print("Video Upload Response:", response)
    print("Status Code:", status)

# Interactive file upload function
def upload_file_interactive():
    """
    Interactive function to upload any file
    """
    file_path = input("Enter the file path (image or video): ")
    tag = input("Enter a tag for the file: ")
    description = input("Enter a description: ")
    
    meta_data = {
        "tag": tag,
        "description": description
    }
    
    # Determine file type based on extension
    file_extension = os.path.splitext(file_path)[1].lower()
    if file_extension in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']:
        meta_data["type"] = "video"
        print("Uploading video...")
    elif file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
        meta_data["type"] = "image"
        print("Uploading image...")
    else:
        meta_data["type"] = "unknown"
        print("Uploading file...")
    
    status, response = UploadFile(file_path, meta_data)
    print(f"\nUpload completed!")
    print(f"Status Code: {status}")
    print(f"Response: {response}")

if __name__ == "__main__":
    print("File Upload Script")
    print("1. Upload image example")
    print("2. Upload video example") 
    print("3. Interactive upload")
    
    choice = input("Choose an option (1-3): ")
    
    if choice == "1":
        upload_image_example()
    elif choice == "2":
        upload_video_example()
    elif choice == "3":
        upload_file_interactive()
    else:
        print("Invalid choice. Running interactive upload...")
        upload_file_interactive()
